/* ===== PREMIUM OCEAN PROFESSIONAL DESIGN - ALL TIME PLUMBING ===== */

/* CSS Variables - Ocean Professional Theme with Seamless Color Flow */
:root {
  /* Primary Ocean Theme - Deep Professional Blues */
  --primary-ocean: #0f172a;
  --primary-ocean-light: #1e293b;
  --primary-ocean-lighter: #334155;
  --primary-deep-blue: #0c4a6e;
  --primary-sky-blue: #0369a1;

  /* Secondary Teal Accents - Fresh & Modern */
  --accent-teal: #0891b2;
  --accent-teal-dark: #0e7490;
  --accent-teal-light: #06b6d4;
  --accent-teal-lighter: #67e8f9;
  --accent-cyan: #22d3ee;

  /* Tertiary Warm Accents - Premium Gold */
  --accent-gold: #f59e0b;
  --accent-gold-dark: #d97706;
  --accent-gold-light: #fcd34d;
  --accent-amber: #fbbf24;

  /* Sophisticated Neutrals with Blue Undertones */
  --white: #ffffff;
  --off-white: #fefefe;
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;

  /* Enhanced Text Colors */
  --text-primary: #0f172a;
  --text-secondary: #334155;
  --text-muted: #64748b;
  --text-light: #94a3b8;
  --text-white: #ffffff;
  --text-white-muted: rgba(255, 255, 255, 0.8);

  /* Status Colors with Ocean Theme */
  --success: #059669;
  --success-light: #10b981;
  --warning: #f59e0b;
  --warning-light: #fbbf24;
  --error: #dc2626;
  --error-light: #ef4444;
  --info: var(--accent-teal);

  /* Premium Gradients - Ocean Flow */
  --gradient-ocean-primary: linear-gradient(
    135deg,
    var(--primary-ocean) 0%,
    var(--primary-ocean-light) 50%,
    var(--primary-deep-blue) 100%
  );
  --gradient-ocean-secondary: linear-gradient(
    135deg,
    var(--primary-deep-blue) 0%,
    var(--primary-sky-blue) 100%
  );
  --gradient-teal-primary: linear-gradient(
    135deg,
    var(--accent-teal-dark) 0%,
    var(--accent-teal) 50%,
    var(--accent-teal-light) 100%
  );
  --gradient-teal-secondary: linear-gradient(
    135deg,
    var(--accent-teal) 0%,
    var(--accent-cyan) 100%
  );
  --gradient-gold-primary: linear-gradient(
    135deg,
    var(--accent-gold-dark) 0%,
    var(--accent-gold) 50%,
    var(--accent-gold-light) 100%
  );
  --gradient-hero: linear-gradient(
    135deg,
    var(--primary-ocean) 0%,
    var(--primary-ocean-light) 30%,
    var(--primary-deep-blue) 70%,
    var(--primary-sky-blue) 100%
  );
  --gradient-card: linear-gradient(
    135deg,
    var(--white) 0%,
    var(--gray-50) 100%
  );
  --gradient-text: linear-gradient(
    135deg,
    var(--accent-teal) 0%,
    var(--accent-cyan) 100%
  );

  /* Typography */
  --font-primary: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    sans-serif;
  --font-display: "Space Grotesk", "Inter", sans-serif;

  /* Enhanced Spacing System */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;
  --space-3xl: 4rem;
  --space-4xl: 6rem;
  --space-5xl: 8rem;

  /* Modern Border Radius */
  --radius-xs: 0.25rem;
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  --radius-3xl: 2rem;
  --radius-full: 9999px;

  /* Premium Ocean-Themed Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(15, 23, 42, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(15, 23, 42, 0.1),
    0 2px 4px -1px rgba(15, 23, 42, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(15, 23, 42, 0.1),
    0 4px 6px -2px rgba(15, 23, 42, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(15, 23, 42, 0.1),
    0 10px 10px -5px rgba(15, 23, 42, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(15, 23, 42, 0.25);
  --shadow-ocean: 0 32px 64px -12px rgba(8, 145, 178, 0.4);
  --shadow-teal: 0 20px 40px -8px rgba(8, 145, 178, 0.3);
  --shadow-gold: 0 20px 40px -8px rgba(245, 158, 11, 0.3);
  --shadow-glow-teal: 0 0 30px rgba(8, 145, 178, 0.2);
  --shadow-glow-gold: 0 0 30px rgba(245, 158, 11, 0.2);
  --shadow-inner: inset 0 2px 4px 0 rgba(15, 23, 42, 0.06);

  /* Smooth Transitions */
  --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-base: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-bounce: 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);

  /* Z-Index Scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* ===== RESET & BASE STYLES ===== */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
  scroll-padding-top: 100px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: var(--font-primary);
  font-size: 1rem;
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--white);
  overflow-x: hidden;
  position: relative;
}

/* ===== TYPOGRAPHY ===== */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--font-display);
  font-weight: 700;
  line-height: 1.2;
  letter-spacing: -0.025em;
  color: var(--gray-900);
}

h1 {
  font-size: clamp(2.5rem, 5vw, 4rem);
}
h2 {
  font-size: clamp(2rem, 4vw, 3rem);
}
h3 {
  font-size: clamp(1.5rem, 3vw, 2rem);
}
h4 {
  font-size: clamp(1.25rem, 2.5vw, 1.5rem);
}
h5 {
  font-size: 1.125rem;
}
h6 {
  font-size: 1rem;
}

p {
  margin-bottom: var(--space-md);
  color: var(--text-secondary);
  font-size: 1.125rem;
  line-height: 1.7;
}

/* ===== UTILITY CLASSES ===== */
.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--space-lg);
}

@media (max-width: 768px) {
  .container {
    padding: 0 var(--space-md);
  }
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* ===== EMERGENCY ALERT BAR ===== */
.emergency-alert {
  background: linear-gradient(
    135deg,
    var(--error) 0%,
    var(--error-light) 50%,
    #dc2626 100%
  );
  color: var(--text-white);
  padding: var(--space-sm) 0;
  position: relative;
  z-index: var(--z-sticky);
  animation: alertPulse 3s ease-in-out infinite;
  box-shadow: var(--shadow-md);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

@keyframes alertPulse {
  0%,
  100% {
    background: linear-gradient(
      135deg,
      var(--error) 0%,
      var(--error-light) 50%,
      #dc2626 100%
    );
    box-shadow: var(--shadow-md);
  }
  50% {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 50%, #991b1b 100%);
    box-shadow: var(--shadow-lg);
  }
}

.alert-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--space-md);
  flex-wrap: wrap;
}

.alert-icon {
  font-size: 1.25rem;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%,
  20%,
  53%,
  80%,
  100% {
    transform: translateY(0);
  }
  40%,
  43% {
    transform: translateY(-8px);
  }
  70% {
    transform: translateY(-4px);
  }
  90% {
    transform: translateY(-2px);
  }
}

.alert-text {
  flex: 1;
  text-align: center;
}

.alert-text strong {
  display: block;
  font-size: 1.125rem;
  font-weight: 800;
  margin-bottom: var(--space-xs);
}

.alert-text span {
  font-size: 0.875rem;
  opacity: 0.9;
}

.alert-actions {
  display: flex;
  align-items: center;
  gap: var(--space-md);
}

.emergency-call-btn {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-sm) var(--space-lg);
  background: rgba(255, 255, 255, 0.15);
  border: 2px solid rgba(255, 255, 255, 0.25);
  border-radius: var(--radius-full);
  color: var(--text-white);
  font-weight: 700;
  text-decoration: none;
  transition: var(--transition-bounce);
  backdrop-filter: blur(15px);
  box-shadow: var(--shadow-sm);
}

.emergency-call-btn:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-3px) scale(1.02);
  box-shadow: var(--shadow-lg);
}

.alert-close {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-full);
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: var(--white);
  cursor: pointer;
  transition: var(--transition-base);
  display: flex;
  align-items: center;
  justify-content: center;
}

.alert-close:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

/* ===== NAVIGATION ===== */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(8, 145, 178, 0.1);
  z-index: var(--z-fixed);
  transition: var(--transition-base);
  padding: var(--space-md) 0;
  box-shadow: var(--shadow-sm);
}

.navbar.scrolled {
  background: var(--gradient-ocean-primary);
  box-shadow: var(--shadow-ocean);
  padding: var(--space-sm) 0;
  backdrop-filter: blur(25px);
  border-bottom: 1px solid rgba(8, 145, 178, 0.2);
}

.navbar.scrolled .nav-link {
  color: var(--text-white) !important;
  font-weight: 600;
}

.navbar.scrolled .nav-link:hover,
.navbar.scrolled .nav-link.active {
  color: var(--accent-teal-light) !important;
  background: rgba(8, 145, 178, 0.15);
  box-shadow: var(--shadow-glow-teal);
}

.navbar.scrolled .logo-text .brand-main {
  color: var(--text-white) !important;
}

.navbar.scrolled .logo-text .brand-sub {
  color: var(--text-white-muted) !important;
}

.navbar.scrolled .hamburger-line {
  background: var(--text-white) !important;
}

.nav-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--space-lg);
}

/* Logo */
.nav-brand .logo {
  display: flex;
  align-items: center;
  gap: var(--space-md);
}

.logo-star {
  width: 48px;
  height: 48px;
  position: relative;
  background: var(--gradient-ocean-secondary);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-teal);
  transition: var(--transition-bounce);
  overflow: hidden;
}

.logo-star::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  animation: logoShine 3s ease-in-out infinite;
}

@keyframes logoShine {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

.logo-star:hover {
  transform: rotate(8deg) scale(1.08);
  box-shadow: var(--shadow-ocean);
}

.star-shape {
  width: 24px;
  height: 24px;
  background: var(--white);
  clip-path: polygon(
    50% 0%,
    61% 35%,
    98% 35%,
    68% 57%,
    79% 91%,
    50% 70%,
    21% 91%,
    32% 57%,
    2% 35%,
    39% 35%
  );
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.logo-text {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.logo-text .brand-main {
  font-family: var(--font-display);
  font-size: 1.5rem;
  font-weight: 900;
  background: var(--gradient-text);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: 0.05em;
  line-height: 1;
}

.logo-text .brand-sub {
  font-family: var(--font-display);
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-muted);
  letter-spacing: 0.1em;
  line-height: 1;
}

/* Navigation Menu */
.nav-menu {
  display: flex;
  align-items: center;
  gap: var(--space-xl);
}

.nav-link {
  font-family: var(--font-display);
  font-weight: 600;
  font-size: 0.875rem;
  color: var(--text-secondary);
  text-decoration: none;
  letter-spacing: 0.025em;
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-lg);
  transition: var(--transition-bounce);
  position: relative;
  overflow: hidden;
}

.nav-link::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: var(--gradient-teal-secondary);
  opacity: 0;
  transition: var(--transition-base);
  z-index: -1;
}

.nav-link:hover::before,
.nav-link.active::before {
  left: 0;
  opacity: 0.1;
}

.nav-link:hover,
.nav-link.active {
  color: var(--accent-teal);
  transform: translateY(-2px);
  box-shadow: var(--shadow-glow-teal);
}

.nav-link.emergency-nav {
  background: var(--gradient-gold-primary);
  color: var(--text-white);
  animation: emergencyGlow 2s ease-in-out infinite;
  box-shadow: var(--shadow-gold);
}

@keyframes emergencyGlow {
  0%,
  100% {
    box-shadow: var(--shadow-gold);
  }
  50% {
    box-shadow: var(--shadow-glow-gold);
    transform: translateY(-1px);
  }
}

.nav-link.emergency-nav:hover {
  background: linear-gradient(
    135deg,
    var(--accent-gold) 0%,
    var(--accent-gold-dark) 100%
  );
  transform: translateY(-3px);
  box-shadow: var(--shadow-glow-gold);
}

/* Dropdown */
.nav-dropdown {
  position: relative;
}

.dropdown-trigger {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.dropdown-trigger i {
  font-size: 0.75rem;
  transition: var(--transition-base);
}

.nav-dropdown:hover .dropdown-trigger i {
  transform: rotate(180deg);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: var(--white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-2xl);
  border: 1px solid var(--gray-200);
  padding: var(--space-lg);
  min-width: 600px;
  opacity: 0;
  visibility: hidden;
  transform: translateX(-50%) translateY(-10px);
  transition: var(--transition-base);
  z-index: var(--z-dropdown);
}

.nav-dropdown:hover .dropdown-menu {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(0);
}

.dropdown-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-lg);
}

.dropdown-section h4 {
  font-size: 0.875rem;
  font-weight: 700;
  background: var(--gradient-text);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: var(--space-md);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.dropdown-link {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  padding: var(--space-md);
  border-radius: var(--radius-xl);
  color: var(--text-secondary);
  text-decoration: none;
  transition: var(--transition-bounce);
  margin-bottom: var(--space-sm);
  position: relative;
  overflow: hidden;
}

.dropdown-link::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: var(--gradient-teal-secondary);
  opacity: 0;
  transition: var(--transition-base);
  z-index: -1;
}

.dropdown-link:hover::before {
  left: 0;
  opacity: 0.08;
}

.dropdown-link:hover {
  color: var(--accent-teal);
  transform: translateX(6px) translateY(-2px);
  box-shadow: var(--shadow-glow-teal);
}

.dropdown-link i {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--gradient-teal-primary);
  color: var(--text-white);
  border-radius: var(--radius-lg);
  font-size: 0.75rem;
  box-shadow: var(--shadow-sm);
  transition: var(--transition-bounce);
}

.dropdown-link:hover i {
  transform: scale(1.1) rotate(5deg);
  box-shadow: var(--shadow-teal);
}

/* CTA Button */
.nav-cta .cta-btn {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-md) var(--space-xl);
  background: var(--gradient-teal-primary);
  color: var(--text-white);
  text-decoration: none;
  border-radius: var(--radius-full);
  font-weight: 700;
  font-size: 0.875rem;
  transition: var(--transition-bounce);
  box-shadow: var(--shadow-teal);
  position: relative;
  overflow: hidden;
}

.nav-cta .cta-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: var(--transition-base);
}

.nav-cta .cta-btn:hover::before {
  left: 100%;
}

.nav-cta .cta-btn:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: var(--shadow-ocean);
  background: var(--gradient-ocean-secondary);
}

/* Mobile Menu Toggle */
.menu-toggle {
  display: none;
  flex-direction: column;
  gap: 4px;
  width: 32px;
  height: 32px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
}

.hamburger-line {
  width: 100%;
  height: 3px;
  background: var(--accent-teal);
  border-radius: var(--radius-full);
  transition: var(--transition-base);
}

.menu-toggle.active .hamburger-line:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.menu-toggle.active .hamburger-line:nth-child(2) {
  opacity: 0;
}

.menu-toggle.active .hamburger-line:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

/* ===== HERO SECTION ===== */
.hero {
  min-height: 100vh;
  position: relative;
  display: flex;
  align-items: center;
  padding: var(--space-4xl) 0 var(--space-3xl);
  overflow: hidden;
}

/* Hero Background */
.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.hero-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.1;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--gradient-hero);
  backdrop-filter: blur(2px);
}

.hero-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

/* Floating Elements */
.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
}

.floating-pipe {
  position: absolute;
  width: 60px;
  height: 4px;
  background: var(--gradient-teal-secondary);
  border-radius: var(--radius-full);
  opacity: 0.15;
  animation: floatPipe 20s linear infinite;
  box-shadow: var(--shadow-glow-teal);
}

.pipe-1 {
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}
.pipe-2 {
  top: 20%;
  right: 15%;
  animation-delay: -3s;
}
.pipe-3 {
  top: 60%;
  left: 5%;
  animation-delay: -6s;
}
.pipe-4 {
  top: 80%;
  right: 20%;
  animation-delay: -9s;
}
.pipe-5 {
  top: 40%;
  left: 80%;
  animation-delay: -12s;
}
.pipe-6 {
  top: 70%;
  right: 10%;
  animation-delay: -15s;
}

@keyframes floatPipe {
  0% {
    transform: translateY(0) rotate(0deg);
  }
  25% {
    transform: translateY(-20px) rotate(90deg);
  }
  50% {
    transform: translateY(0) rotate(180deg);
  }
  75% {
    transform: translateY(-15px) rotate(270deg);
  }
  100% {
    transform: translateY(0) rotate(360deg);
  }
}

/* Hero Content */
.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-4xl);
  align-items: center;
  position: relative;
  z-index: 1;
}

.hero-text {
  max-width: 600px;
}

/* Hero Badge */
.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-sm) var(--space-lg);
  background: rgba(255, 255, 255, 0.12);
  border: 1px solid rgba(8, 145, 178, 0.3);
  border-radius: var(--radius-full);
  color: var(--text-white);
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: var(--space-xl);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(15px);
  box-shadow: var(--shadow-glow-teal);
}

.badge-glow {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: badgeGlow 3s ease-in-out infinite;
}

@keyframes badgeGlow {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

.badge-pulse {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-full);
  transform: translate(-50%, -50%);
  animation: badgePulse 2s ease-in-out infinite;
}

@keyframes badgePulse {
  0%,
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.5;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0;
  }
}

/* Hero Title */
.hero-title {
  font-size: clamp(3rem, 6vw, 5rem);
  font-weight: 900;
  line-height: 1.1;
  margin-bottom: var(--space-xl);
  position: relative;
}

.title-line {
  display: block;
  color: var(--text-white);
}

.title-line.highlight {
  background: var(--gradient-text);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
  filter: drop-shadow(0 2px 4px rgba(8, 145, 178, 0.3));
}

.title-decoration {
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 120px;
  height: 4px;
  background: var(--gradient-teal-secondary);
  border-radius: var(--radius-full);
  box-shadow: var(--shadow-glow-teal);
}

/* Hero Subtitle */
.hero-subtitle {
  font-size: 1.25rem;
  line-height: 1.7;
  color: var(--text-white-muted);
  margin-bottom: var(--space-2xl);
}

.hero-subtitle strong {
  color: var(--accent-teal-lighter);
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(8, 145, 178, 0.3);
}

/* Hero Features */
.hero-features {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
  margin-bottom: var(--space-2xl);
}

.feature-item {
  display: flex;
  align-items: center;
  gap: var(--space-lg);
  padding: var(--space-lg);
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(8, 145, 178, 0.2);
  border-radius: var(--radius-2xl);
  backdrop-filter: blur(20px);
  transition: var(--transition-bounce);
  position: relative;
  overflow: hidden;
}

.feature-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: var(--gradient-teal-secondary);
  opacity: 0;
  transition: var(--transition-base);
  z-index: -1;
}

.feature-item:hover::before {
  left: 0;
  opacity: 0.05;
}

.feature-item:hover {
  transform: translateX(12px) translateY(-4px);
  box-shadow: var(--shadow-ocean);
  border-color: var(--accent-teal-light);
  background: rgba(255, 255, 255, 1);
}

.feature-icon {
  width: 48px;
  height: 48px;
  background: var(--gradient-ocean-secondary);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-white);
  font-size: 1.25rem;
  flex-shrink: 0;
  box-shadow: var(--shadow-teal);
  transition: var(--transition-bounce);
}

.feature-item:hover .feature-icon {
  transform: scale(1.1) rotate(5deg);
  box-shadow: var(--shadow-ocean);
}

.feature-content {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.feature-title {
  font-weight: 700;
  color: var(--text-primary);
  font-size: 1.125rem;
}

.feature-desc {
  color: var(--text-primary);
  font-size: 0.875rem;
  opacity: 0.9;
}

/* Hero Actions */
.hero-actions {
  display: flex;
  gap: var(--space-lg);
  margin-bottom: var(--space-2xl);
}

.btn {
  position: relative;
  display: inline-flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-lg) var(--space-2xl);
  border-radius: var(--radius-full);
  font-weight: 700;
  font-size: 1.125rem;
  text-decoration: none;
  transition: var(--transition-bounce);
  overflow: hidden;
  border: none;
  cursor: pointer;
}

.btn-premium {
  position: relative;
  overflow: hidden;
}

.btn-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transition: var(--transition-base);
}

.btn-content {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.btn-primary.btn-premium {
  background: var(--gradient-ocean-primary);
  color: var(--text-white);
  box-shadow: var(--shadow-ocean);
}

.btn-primary.btn-premium:hover {
  background: var(--gradient-teal-primary);
  transform: translateY(-4px) scale(1.02);
  box-shadow: var(--shadow-glow-teal);
}

.btn-secondary.btn-premium {
  background: rgba(255, 255, 255, 0.15);
  color: var(--text-white);
  border: 2px solid rgba(8, 145, 178, 0.3);
  box-shadow: var(--shadow-lg);
}

.btn-secondary.btn-premium:hover {
  background: var(--gradient-teal-secondary);
  border-color: var(--accent-teal-light);
  transform: translateY(-4px) scale(1.02);
  box-shadow: var(--shadow-glow-teal);
}

.btn-primary.btn-premium:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-2xl);
}

.btn-primary.btn-premium .btn-bg {
  background: linear-gradient(
    135deg,
    var(--primary-teal-dark) 0%,
    var(--accent-blue-dark) 100%
  );
  opacity: 0;
}

.btn-primary.btn-premium:hover .btn-bg {
  opacity: 1;
}

.btn-glow {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
    circle,
    rgba(8, 145, 178, 0.4) 0%,
    transparent 70%
  );
  opacity: 0;
  transition: var(--transition-base);
}

.btn-primary.btn-premium:hover .btn-glow {
  opacity: 1;
  animation: glowPulse 1.5s ease-in-out infinite;
}

@keyframes glowPulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 0.4;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

.btn-secondary.btn-premium {
  background: transparent;
  color: var(--text-white);
  border: 2px solid rgba(8, 145, 178, 0.5);
}

.btn-secondary.btn-premium:hover {
  transform: translateY(-4px) scale(1.02);
  color: var(--text-white);
  box-shadow: var(--shadow-glow-teal);
}

.btn-secondary.btn-premium .btn-bg {
  background: var(--gradient-teal-primary);
  opacity: 0;
}

.btn-secondary.btn-premium:hover .btn-bg {
  opacity: 1;
}

.btn-trail {
  position: absolute;
  top: 50%;
  right: -100%;
  width: 100%;
  height: 2px;
  background: linear-gradient(
    90deg,
    transparent,
    var(--accent-teal),
    transparent
  );
  transform: translateY(-50%);
  transition: var(--transition-base);
}

.btn-secondary.btn-premium:hover .btn-trail {
  right: 100%;
}

/* Hero Visual */
.hero-visual {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.visual-container {
  position: relative;
  width: 100%;
  max-width: 500px;
}

.main-image {
  position: relative;
  width: 100%;
  height: 400px;
  border-radius: var(--radius-2xl);
  overflow: hidden;
}

.image-frame {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--gray-100) 0%, var(--gray-200) 100%);
  border: 3px solid var(--white);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-2xl);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.image-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-lg);
  color: var(--accent-teal);
  text-align: center;
}

.image-placeholder i {
  font-size: 4rem;
  opacity: 0.7;
}

.image-placeholder span {
  font-size: 1.25rem;
  font-weight: 600;
  opacity: 0.8;
}

.image-glow {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(
    circle,
    rgba(8, 145, 178, 0.15) 0%,
    transparent 70%
  );
  animation: imageGlow 4s ease-in-out infinite;
}

@keyframes imageGlow {
  0%,
  100% {
    transform: scale(1) rotate(0deg);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.1) rotate(180deg);
    opacity: 0.6;
  }
}

/* Floating Stats */
.floating-stats {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.stat-card {
  position: absolute;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(8, 145, 178, 0.2);
  border-radius: var(--radius-2xl);
  padding: var(--space-lg);
  box-shadow: var(--shadow-teal);
  display: flex;
  align-items: center;
  gap: var(--space-md);
  animation: floatStat 6s ease-in-out infinite;
}

.stat-1 {
  top: 10%;
  right: -20%;
  animation-delay: 0s;
}

.stat-2 {
  bottom: 30%;
  left: -25%;
  animation-delay: -2s;
}

.stat-3 {
  top: 60%;
  right: -15%;
  animation-delay: -4s;
}

@keyframes floatStat {
  0%,
  100% {
    transform: translateY(0) scale(1);
  }
  50% {
    transform: translateY(-15px) scale(1.05);
  }
}

.stat-icon {
  width: 40px;
  height: 40px;
  background: var(--gradient-teal-primary);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-white);
  font-size: 1rem;
  box-shadow: var(--shadow-teal);
}

.stat-content {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.stat-number {
  font-size: 1.5rem;
  font-weight: 900;
  background: var(--gradient-text);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1;
}

.stat-label {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--gray-600);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Scroll Indicator */
.scroll-indicator {
  position: absolute;
  bottom: var(--space-2xl);
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-md);
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.875rem;
  font-weight: 500;
}

.scroll-mouse {
  width: 24px;
  height: 40px;
  border: 2px solid rgba(255, 255, 255, 0.6);
  border-radius: var(--radius-full);
  position: relative;
  opacity: 0.7;
}

.scroll-wheel {
  width: 4px;
  height: 8px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: var(--radius-full);
  position: absolute;
  top: 8px;
  left: 50%;
  transform: translateX(-50%);
  animation: scrollWheel 2s ease-in-out infinite;
}

@keyframes scrollWheel {
  0%,
  100% {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
  }
  50% {
    transform: translateX(-50%) translateY(12px);
    opacity: 0.3;
  }
}

/* ===== ABOUT SECTION ===== */
.about {
  padding: var(--space-5xl) 0;
  background: var(--gradient-card);
  position: relative;
  overflow: hidden;
}

.about::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
      circle at 20% 80%,
      rgba(8, 145, 178, 0.05) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 20%,
      rgba(12, 74, 110, 0.05) 0%,
      transparent 50%
    );
  pointer-events: none;
}

.about-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-4xl);
  margin-top: var(--space-4xl);
  align-items: center;
  position: relative;
  z-index: 1;
}

.about-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-lg);
  margin-bottom: var(--space-2xl);
}

.stat-item {
  text-align: center;
  padding: var(--space-lg);
  background: var(--white);
  border-radius: var(--radius-2xl);
  border: 1px solid rgba(8, 145, 178, 0.1);
  transition: var(--transition-bounce);
  position: relative;
  overflow: hidden;
}

.stat-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: var(--gradient-teal-secondary);
  opacity: 0;
  transition: var(--transition-base);
  z-index: -1;
}

.stat-item:hover::before {
  left: 0;
  opacity: 0.05;
}

.stat-item:hover {
  transform: translateY(-6px) scale(1.02);
  box-shadow: var(--shadow-ocean);
  border-color: var(--accent-teal-light);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 900;
  background: var(--gradient-text);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1;
  margin-bottom: var(--space-sm);
}

.stat-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.about-description {
  margin-bottom: var(--space-2xl);
}

.about-description p {
  color: var(--text-primary);
  line-height: 1.8;
  margin-bottom: var(--space-lg);
  opacity: 0.9;
}

.about-credentials {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

.credential-item {
  display: flex;
  align-items: center;
  gap: var(--space-lg);
  padding: var(--space-lg);
  background: var(--white);
  border-radius: var(--radius-2xl);
  border: 1px solid rgba(8, 145, 178, 0.1);
  transition: var(--transition-bounce);
  position: relative;
  overflow: hidden;
}

.credential-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: var(--gradient-teal-secondary);
  opacity: 0;
  transition: var(--transition-base);
  z-index: -1;
}

.credential-item:hover::before {
  left: 0;
  opacity: 0.05;
}

.credential-item:hover {
  border-color: var(--accent-teal-light);
  transform: translateX(12px) translateY(-2px);
  box-shadow: var(--shadow-teal);
}

.credential-icon {
  width: 48px;
  height: 48px;
  background: var(--gradient-ocean-secondary);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-white);
  font-size: 1.25rem;
  flex-shrink: 0;
  box-shadow: var(--shadow-teal);
  transition: var(--transition-bounce);
}

.credential-item:hover .credential-icon {
  transform: scale(1.1) rotate(5deg);
  box-shadow: var(--shadow-ocean);
}

.credential-text h4 {
  font-size: 1.125rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-xs);
}

.credential-text p {
  font-size: 0.875rem;
  color: var(--text-muted);
  margin: 0;
}

.about-visual {
  position: relative;
}

.about-image {
  width: 100%;
  height: 400px;
  background: var(--gray-100);
  border-radius: var(--radius-2xl);
  border: 3px solid var(--white);
  box-shadow: var(--shadow-2xl);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--space-xl);
  overflow: hidden;
  position: relative;
}

.about-image .image-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-lg);
  color: var(--primary-blue);
  text-align: center;
}

.about-image .image-placeholder i {
  font-size: 4rem;
  opacity: 0.7;
}

.about-image .image-placeholder span {
  font-size: 1.25rem;
  font-weight: 600;
  opacity: 0.8;
}

.about-features {
  display: flex;
  gap: var(--space-lg);
}

.feature-highlight {
  flex: 1;
  display: flex;
  align-items: center;
  gap: var(--space-md);
  padding: var(--space-lg);
  background: var(--white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-200);
  transition: var(--transition-base);
}

.feature-highlight:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-blue);
}

.feature-highlight .feature-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(
    135deg,
    var(--accent-teal) 0%,
    var(--accent-teal-dark) 100%
  );
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-size: 1rem;
  flex-shrink: 0;
}

.feature-highlight .feature-content h4 {
  font-size: 1rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-xs);
}

.feature-highlight .feature-content p {
  font-size: 0.875rem;
  color: var(--text-muted);
  margin: 0;
}

/* ===== SERVICES SECTION ===== */
.services {
  padding: var(--space-5xl) 0;
  background: var(--primary-ocean);
  position: relative;
  overflow: hidden;
}

.services::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
      circle at 30% 70%,
      rgba(8, 145, 178, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 70% 30%,
      rgba(3, 105, 161, 0.1) 0%,
      transparent 50%
    );
  pointer-events: none;
}

.section-header {
  text-align: center;
  max-width: 800px;
  margin: 0 auto var(--space-4xl);
  position: relative;
  z-index: 1;
}

.section-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-sm) var(--space-lg);
  background: rgba(8, 145, 178, 0.15);
  border: 1px solid rgba(8, 145, 178, 0.3);
  border-radius: var(--radius-full);
  color: var(--accent-teal-light);
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: var(--space-lg);
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-glow-teal);
}

.section-title {
  font-size: clamp(2.5rem, 5vw, 3.5rem);
  font-weight: 900;
  line-height: 1.1;
  margin-bottom: var(--space-lg);
  color: var(--text-white);
}

.title-accent {
  background: var(--gradient-text);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 2px 4px rgba(8, 145, 178, 0.3));
}

.section-subtitle {
  font-size: 1.25rem;
  line-height: 1.7;
  color: var(--text-white-muted);
  max-width: 600px;
  margin: 0 auto;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--space-2xl);
  margin-top: var(--space-4xl);
  position: relative;
  z-index: 1;
}

.service-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: var(--radius-3xl);
  padding: var(--space-2xl);
  box-shadow: var(--shadow-ocean);
  border: 1px solid rgba(8, 145, 178, 0.2);
  transition: var(--transition-bounce);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(20px);
}

.service-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: var(--gradient-teal-secondary);
  box-shadow: var(--shadow-glow-teal);
}

.service-card::after {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: var(--gradient-teal-secondary);
  opacity: 0;
  transition: var(--transition-base);
  z-index: -1;
}

.service-card:hover::after {
  left: 0;
  opacity: 0.03;
}

.service-card:hover {
  transform: translateY(-12px) scale(1.02);
  box-shadow: var(--shadow-glow-teal);
  border-color: var(--accent-teal-light);
  background: rgba(255, 255, 255, 1);
}

.service-icon {
  width: 64px;
  height: 64px;
  background: var(--gradient-ocean-secondary);
  border-radius: var(--radius-2xl);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-white);
  font-size: 1.5rem;
  margin-bottom: var(--space-lg);
  box-shadow: var(--shadow-teal);
  transition: var(--transition-bounce);
}

.service-card:hover .service-icon {
  transform: scale(1.1) rotate(5deg);
  box-shadow: var(--shadow-ocean);
}

.service-content h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-md);
}

.service-content p {
  color: var(--text-primary);
  line-height: 1.7;
  margin-bottom: var(--space-lg);
  opacity: 0.9;
}

.service-features {
  list-style: none;
  margin-bottom: var(--space-xl);
}

.service-features li {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  color: var(--text-primary);
  margin-bottom: var(--space-sm);
  font-size: 0.875rem;
  opacity: 0.9;
}

.service-features li::before {
  content: "✓";
  color: var(--accent-teal);
  font-weight: 700;
  font-size: 1rem;
  text-shadow: 0 1px 2px rgba(8, 145, 178, 0.3);
}

.service-cta {
  margin-top: auto;
}

.service-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-md) var(--space-lg);
  background: transparent;
  color: var(--accent-teal);
  border: 2px solid var(--accent-teal);
  border-radius: var(--radius-full);
  text-decoration: none;
  font-weight: 600;
  transition: var(--transition-bounce);
  position: relative;
  overflow: hidden;
}

.service-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: var(--gradient-teal-primary);
  transition: var(--transition-base);
  z-index: 1;
}

.service-btn span,
.service-btn i {
  position: relative;
  z-index: 2;
}

.service-btn:hover {
  color: var(--text-white);
  transform: translateY(-3px) scale(1.02);
  box-shadow: var(--shadow-glow-teal);
}

.service-btn:hover::before {
  left: 0;
}

.service-btn.emergency {
  background: var(--gradient-gold-primary);
  color: var(--text-white);
  border-color: var(--accent-gold);
  animation: emergencyPulse 2s ease-in-out infinite;
  box-shadow: var(--shadow-gold);
}

@keyframes emergencyPulse {
  0%,
  100% {
    box-shadow: var(--shadow-gold);
  }
  50% {
    box-shadow: var(--shadow-glow-gold);
    transform: translateY(-1px);
  }
}

.service-btn.emergency:hover {
  background: linear-gradient(
    135deg,
    var(--accent-gold-dark) 0%,
    var(--accent-gold) 100%
  );
  transform: translateY(-4px) scale(1.05);
}

/* ===== GALLERY SECTION ===== */
.gallery {
  padding: var(--space-5xl) 0;
  background: var(--gradient-card);
  position: relative;
  overflow: hidden;
}

.gallery::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
      circle at 70% 20%,
      rgba(8, 145, 178, 0.05) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 30% 80%,
      rgba(12, 74, 110, 0.05) 0%,
      transparent 50%
    );
  pointer-events: none;
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--space-2xl);
  margin-top: var(--space-4xl);
}

.gallery-item {
  position: relative;
  border-radius: var(--radius-3xl);
  overflow: hidden;
  box-shadow: var(--shadow-ocean);
  transition: var(--transition-bounce);
  border: 1px solid rgba(8, 145, 178, 0.1);
}

.gallery-item:hover {
  transform: translateY(-12px) scale(1.02);
  box-shadow: var(--shadow-glow-teal);
  border-color: var(--accent-teal-light);
}

.gallery-image {
  position: relative;
  width: 100%;
  height: 300px;
  background: var(--gradient-card);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.gallery-image .image-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-md);
  color: var(--accent-teal);
  text-align: center;
  z-index: 1;
}

.gallery-image .image-placeholder i {
  font-size: 3rem;
  opacity: 0.7;
}

.gallery-image .image-placeholder span {
  font-size: 1.125rem;
  font-weight: 600;
  opacity: 0.8;
}

.gallery-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--gradient-ocean-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: var(--transition-base);
  backdrop-filter: blur(10px);
}

.gallery-item:hover .gallery-overlay {
  opacity: 1;
}

.gallery-content {
  text-align: center;
  color: var(--text-white);
  padding: var(--space-lg);
}

.gallery-content h4 {
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: var(--space-sm);
  color: var(--text-white);
}

.gallery-content p {
  font-size: 0.875rem;
  opacity: 0.9;
  margin: 0;
  color: var(--text-white-muted);
}

/* ===== CONTACT SECTION ===== */
.contact {
  padding: var(--space-5xl) 0;
  background: var(--primary-ocean);
  position: relative;
  overflow: hidden;
}

.contact::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
      circle at 20% 80%,
      rgba(8, 145, 178, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 20%,
      rgba(3, 105, 161, 0.1) 0%,
      transparent 50%
    );
  pointer-events: none;
}

.contact-container {
  position: relative;
  z-index: 2;
}

.contact-hero {
  text-align: center;
  margin-bottom: var(--space-4xl);
  position: relative;
  z-index: 1;
}

.contact-hero .section-badge {
  background: var(--gradient-teal-primary);
  color: var(--text-white);
  border: 1px solid rgba(8, 145, 178, 0.3);
  padding: var(--space-md) var(--space-xl);
  font-weight: 700;
  box-shadow: var(--shadow-glow-teal);
  backdrop-filter: blur(15px);
}

.contact-hero h2 {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 900;
  color: var(--text-white);
  margin: var(--space-lg) 0;
  line-height: 1.1;
}

.contact-hero .title-accent {
  background: var(--gradient-text);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 2px 4px rgba(8, 145, 178, 0.3));
}

.contact-hero p {
  font-size: 1.25rem;
  color: var(--text-white-muted);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.7;
}

.contact-main {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-4xl);
  margin-bottom: var(--space-4xl);
  position: relative;
  z-index: 1;
}

.contact-info-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: var(--radius-3xl);
  padding: var(--space-3xl);
  box-shadow: var(--shadow-ocean);
  border: 1px solid rgba(8, 145, 178, 0.2);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(20px);
}

.contact-info-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: var(--gradient-teal-secondary);
  box-shadow: var(--shadow-glow-teal);
}

.contact-info-section h3 {
  font-size: 2rem;
  font-weight: 800;
  color: var(--text-primary);
  margin-bottom: var(--space-xl);
  display: flex;
  align-items: center;
  gap: var(--space-md);
}

.contact-info-section h3::before {
  content: "📞";
  font-size: 1.5rem;
}

.contact-cards {
  display: grid;
  gap: var(--space-lg);
  margin-bottom: var(--space-2xl);
}

.contact-card {
  background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
  border-radius: var(--radius-2xl);
  padding: var(--space-xl);
  border: 2px solid transparent;
  transition: var(--transition-base);
  position: relative;
  overflow: hidden;
}

.contact-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(30, 64, 175, 0.05) 0%,
    rgba(0, 139, 139, 0.05) 100%
  );
  opacity: 0;
  transition: var(--transition-base);
}

.contact-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-2xl);
  border-color: var(--primary-blue);
}

.contact-card:hover::before {
  opacity: 1;
}

.contact-card-content {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  gap: var(--space-lg);
}

.contact-card-icon {
  width: 64px;
  height: 64px;
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-size: 1.5rem;
  flex-shrink: 0;
  box-shadow: var(--shadow-lg);
}

.contact-card-icon.phone {
  background: linear-gradient(
    135deg,
    var(--primary-blue) 0%,
    var(--primary-blue-dark) 100%
  );
}

.contact-card-icon.email {
  background: linear-gradient(
    135deg,
    var(--accent-teal) 0%,
    var(--accent-teal-dark) 100%
  );
}

.contact-card-icon.location {
  background: linear-gradient(135deg, var(--success) 0%, #059669 100%);
}

.contact-card-icon.hours {
  background: linear-gradient(135deg, var(--warning) 0%, #d97706 100%);
}

.contact-card-details h4 {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-xs);
}

.contact-card-value {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--primary-blue);
  margin-bottom: var(--space-xs);
}

.contact-card-subtitle {
  font-size: 0.875rem;
  color: var(--text-primary);
  opacity: 0.8;
}

.social-connect {
  background: linear-gradient(
    135deg,
    var(--primary-blue) 0%,
    var(--primary-blue-dark) 100%
  );
  border-radius: var(--radius-2xl);
  padding: var(--space-xl);
  text-align: center;
  color: var(--white);
}

.social-connect h4 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: var(--space-lg);
}

.social-connect p {
  margin-bottom: var(--space-lg);
  opacity: 0.9;
}

.social-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-md);
}

.social-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-xl);
  padding: var(--space-lg);
  text-decoration: none;
  color: var(--white);
  transition: var(--transition-base);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.social-card:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.social-card i {
  font-size: 2rem;
  margin-bottom: var(--space-sm);
  display: block;
}

.social-card span {
  font-size: 0.875rem;
  font-weight: 600;
}

/* Contact Form */
.contact-form-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: var(--radius-3xl);
  padding: var(--space-2xl);
  border: 1px solid rgba(8, 145, 178, 0.2);
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-ocean);
}

.form-container h3 {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-xl);
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-lg);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.form-group label {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: var(--space-md);
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-xl);
  font-size: 1rem;
  transition: var(--transition-bounce);
  background: var(--white);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--accent-teal);
  box-shadow: 0 0 0 3px rgba(8, 145, 178, 0.15);
  transform: translateY(-2px);
}

.form-submit {
  background: linear-gradient(
    135deg,
    var(--primary-blue) 0%,
    var(--primary-blue-dark) 100%
  );
  color: var(--white);
  border: none;
  padding: var(--space-lg) var(--space-2xl);
  border-radius: var(--radius-full);
  font-weight: 700;
  font-size: 1.125rem;
  cursor: pointer;
  transition: var(--transition-base);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-sm);
  margin-top: var(--space-lg);
}

.form-submit:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.form-error {
  color: var(--error);
  font-size: 0.875rem;
  margin-top: var(--space-xs);
  min-height: 1.25rem;
  display: block;
}

/* Emergency CTA */
.emergency-section {
  margin-top: var(--space-4xl);
  position: relative;
  overflow: hidden;
  border-radius: var(--radius-3xl);
  background: linear-gradient(135deg, #ff4444 0%, #cc1111 50%, #990000 100%);
  box-shadow: var(--shadow-2xl), 0 0 50px rgba(255, 68, 68, 0.3);
  animation: emergencyGlow 3s ease-in-out infinite;
}

@keyframes emergencyGlow {
  0%,
  100% {
    box-shadow: var(--shadow-2xl), 0 0 50px rgba(255, 68, 68, 0.3);
  }
  50% {
    box-shadow: var(--shadow-2xl), 0 0 80px rgba(255, 68, 68, 0.5);
  }
}

.emergency-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
      circle at 30% 70%,
      rgba(255, 255, 255, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 70% 30%,
      rgba(255, 255, 255, 0.05) 0%,
      transparent 50%
    );
  animation: emergencyShimmer 4s ease-in-out infinite;
}

@keyframes emergencyShimmer {
  0%,
  100% {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
}

.emergency-content {
  position: relative;
  z-index: 2;
  padding: var(--space-3xl);
  text-align: center;
  color: var(--white);
}

.emergency-icon {
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--space-xl);
  font-size: 2.5rem;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  animation: emergencyPulse 2s ease-in-out infinite;
}

@keyframes emergencyPulse {
  0%,
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 20px rgba(255, 255, 255, 0);
  }
}

.emergency-content h3 {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 900;
  margin-bottom: var(--space-lg);
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.emergency-content p {
  font-size: 1.25rem;
  margin-bottom: var(--space-2xl);
  opacity: 0.95;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

.emergency-cta {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-lg);
}

.btn-emergency-call {
  display: inline-flex;
  align-items: center;
  gap: var(--space-md);
  padding: var(--space-xl) var(--space-3xl);
  background: rgba(255, 255, 255, 0.95);
  color: #cc1111;
  border: none;
  border-radius: var(--radius-full);
  font-weight: 900;
  font-size: 1.5rem;
  text-decoration: none;
  transition: var(--transition-base);
  box-shadow: var(--shadow-xl);
  backdrop-filter: blur(10px);
}

.btn-emergency-call:hover {
  background: var(--white);
  transform: translateY(-4px) scale(1.05);
  box-shadow: var(--shadow-2xl);
  color: #990000;
}

.btn-emergency-call i {
  font-size: 1.75rem;
  animation: phoneRing 1.5s ease-in-out infinite;
}

@keyframes phoneRing {
  0%,
  100% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(-15deg);
  }
  75% {
    transform: rotate(15deg);
  }
}

.emergency-note {
  font-size: 0.875rem;
  opacity: 0.9;
  font-style: italic;
}

/* ===== FOOTER ===== */
.footer {
  background: var(--gradient-ocean-primary);
  color: var(--text-white);
  padding: var(--space-5xl) 0 var(--space-lg);
  position: relative;
  overflow: hidden;
}

.footer::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
      circle at 20% 80%,
      rgba(8, 145, 178, 0.15) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 20%,
      rgba(3, 105, 161, 0.15) 0%,
      transparent 50%
    );
  pointer-events: none;
}

@keyframes footerFloat {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-10px) rotate(1deg);
  }
}

.footer-container {
  position: relative;
  z-index: 2;
}

.footer-main {
  margin-bottom: var(--space-3xl);
}

.footer-grid {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1.5fr;
  gap: var(--space-4xl);
}

.footer-section {
  position: relative;
}

.footer-section h4,
.footer-section h5 {
  font-size: 1.25rem;
  font-weight: 800;
  margin-bottom: var(--space-lg);
  color: var(--text-white);
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.footer-section h4::before {
  content: "";
  width: 4px;
  height: 20px;
  background: var(--gradient-teal-secondary);
  border-radius: var(--radius-sm);
  box-shadow: var(--shadow-glow-teal);
}

.footer-brand {
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--radius-2xl);
  padding: var(--space-xl);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  margin-bottom: var(--space-lg);
}

.footer-logo .logo-icon {
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, var(--accent-teal) 0%, #00b8b8 100%);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-lg);
  color: var(--white);
  font-size: 1.25rem;
}

.footer-logo .logo-text h3 {
  font-size: 1.5rem;
  font-weight: 900;
  color: var(--white);
  margin: 0;
}

.footer-logo .logo-text p {
  font-size: 0.875rem;
  color: var(--gray-400);
  margin: 0;
}

.company-description {
  color: var(--gray-300);
  line-height: 1.6;
  margin-bottom: var(--space-lg);
}

.company-credentials {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.credential {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  color: var(--gray-300);
  font-size: 0.875rem;
}

.credential i {
  color: var(--primary-blue);
}

.footer-links {
  list-style: none;
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.footer-links a {
  color: var(--gray-300);
  text-decoration: none;
  transition: var(--transition-base);
  font-size: 0.875rem;
}

.footer-links a:hover {
  color: var(--primary-blue);
  transform: translateX(4px);
}

.footer-contact-items {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
  margin-bottom: var(--space-lg);
}

.footer-contact-item {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  color: inherit;
  text-decoration: none;
  transition: var(--transition-base);
}

.footer-contact-item:hover {
  color: var(--primary-blue);
}

.footer-contact-icon {
  width: 36px;
  height: 36px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  flex-shrink: 0;
}

.footer-contact-icon.phone {
  background: var(--gradient-ocean-secondary);
  box-shadow: var(--shadow-teal);
}

.footer-contact-icon.email {
  background: var(--gradient-teal-primary);
  box-shadow: var(--shadow-teal);
}

.footer-contact-icon.location {
  background: linear-gradient(
    135deg,
    var(--success) 0%,
    var(--success-light) 100%
  );
  box-shadow: 0 4px 12px rgba(5, 150, 105, 0.3);
}

.footer-contact-icon.hours {
  background: var(--gradient-gold-primary);
  box-shadow: var(--shadow-gold);
}

.footer-contact-details .contact-value {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--white);
  margin-bottom: 2px;
}

.footer-contact-details .contact-subtitle {
  font-size: 0.75rem;
  color: var(--gray-400);
}

.footer-social-links {
  display: flex;
  gap: var(--space-sm);
}

.footer-social-link {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  text-decoration: none;
  transition: var(--transition-base);
  font-size: 1rem;
}

.footer-social-link:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.footer-bottom {
  border-top: 1px solid var(--gray-700);
  padding-top: var(--space-lg);
}

.footer-bottom-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.copyright {
  color: var(--gray-400);
  font-size: 0.875rem;
}

.scroll-to-top-btn {
  width: 44px;
  height: 44px;
  background: var(--gradient-ocean-secondary);
  border: none;
  border-radius: var(--radius-full);
  color: var(--text-white);
  cursor: pointer;
  transition: var(--transition-bounce);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.125rem;
  box-shadow: var(--shadow-teal);
}

.scroll-to-top-btn:hover {
  transform: translateY(-6px) scale(1.1);
  box-shadow: var(--shadow-glow-teal);
  background: var(--gradient-teal-primary);
}

/* ===== RESPONSIVE DESIGN ===== */

/* Tablet Styles */
@media (max-width: 1024px) {
  .hero-content {
    grid-template-columns: 1fr;
    gap: var(--space-3xl);
    text-align: center;
  }

  .hero-text {
    max-width: 100%;
  }

  .dropdown-menu {
    min-width: 500px;
  }

  .dropdown-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .hero-actions {
    justify-content: center;
  }

  .floating-stats .stat-card {
    position: relative;
    top: auto;
    left: auto;
    right: auto;
    bottom: auto;
    margin: var(--space-md);
    display: inline-flex;
  }

  .floating-stats {
    position: relative;
    display: flex;
    justify-content: center;
    gap: var(--space-md);
    margin-top: var(--space-xl);
    pointer-events: auto;
  }
}

/* Mobile Styles */
@media (max-width: 768px) {
  .nav-menu {
    display: none;
  }

  .menu-toggle {
    display: flex;
  }

  .nav-menu {
    position: fixed;
    top: 0;
    right: 0;
    width: 100%;
    height: 100vh;
    background: var(--gradient-ocean-primary);
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: var(--space-xl);
    transform: translateX(100%);
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-base);
    z-index: var(--z-fixed);
  }

  .nav-menu.active {
    transform: translateX(0);
    opacity: 1;
    visibility: visible;
  }

  .nav-link {
    color: var(--text-white);
    font-size: 1.5rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    padding: var(--space-lg);
    border-radius: var(--radius-lg);
    transition: var(--transition-base);
  }

  .nav-link:hover,
  .nav-link.active {
    background: rgba(8, 145, 178, 0.2);
    color: var(--accent-teal-lighter);
    transform: translateY(-2px);
    box-shadow: var(--shadow-glow-teal);
  }

  .nav-dropdown {
    position: relative;
  }

  .dropdown-menu {
    position: static;
    opacity: 1;
    visibility: visible;
    transform: none;
    background: transparent;
    box-shadow: none;
    border: none;
    padding: 0;
    min-width: auto;
    margin-top: var(--space-md);
  }

  .dropdown-grid {
    display: flex;
    flex-direction: column;
    gap: var(--space-md);
  }

  .dropdown-section {
    text-align: center;
  }

  .dropdown-link {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1rem;
    padding: var(--space-sm);
  }

  .nav-cta {
    display: none;
  }

  .hero {
    padding: var(--space-3xl) 0 var(--space-2xl);
    min-height: auto;
  }

  .hero-title {
    font-size: clamp(2rem, 8vw, 3rem);
  }

  .hero-features {
    gap: var(--space-md);
  }

  .feature-item {
    padding: var(--space-md);
    gap: var(--space-md);
  }

  .feature-icon {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
    gap: var(--space-md);
  }

  .btn {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }

  .main-image {
    height: 300px;
  }

  .dropdown-menu {
    min-width: 300px;
    left: 0;
    transform: translateX(0) translateY(-10px);
  }

  .nav-dropdown:hover .dropdown-menu {
    transform: translateX(0) translateY(0);
  }

  .dropdown-grid {
    grid-template-columns: 1fr;
    gap: var(--space-md);
  }

  .alert-content {
    flex-direction: column;
    text-align: center;
    gap: var(--space-sm);
  }

  .alert-actions {
    justify-content: center;
  }

  .floating-stats {
    flex-direction: column;
    align-items: center;
  }

  .navbar {
    padding: var(--space-sm) 0;
  }

  .nav-content {
    gap: var(--space-md);
  }

  .nav-cta {
    display: none;
  }

  .services-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-xl);
  }

  .service-card {
    padding: var(--space-xl);
  }

  .contact-grid {
    grid-template-columns: 1fr;
    gap: var(--space-2xl);
  }

  .footer-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-2xl);
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .about-content {
    grid-template-columns: 1fr;
    gap: var(--space-2xl);
  }

  .about-stats {
    grid-template-columns: 1fr;
    gap: var(--space-md);
  }

  .gallery-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-xl);
  }
}

/* Small Mobile Styles */
@media (max-width: 480px) {
  .container {
    padding: 0 var(--space-sm);
  }

  .hero-badge {
    font-size: 0.75rem;
    padding: var(--space-xs) var(--space-md);
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .feature-item {
    flex-direction: column;
    text-align: center;
    gap: var(--space-sm);
  }

  .btn {
    padding: var(--space-md) var(--space-lg);
    font-size: 1rem;
  }

  .logo-star {
    width: 40px;
    height: 40px;
  }

  .star-shape {
    width: 20px;
    height: 20px;
  }

  .logo-text .brand-main {
    font-size: 1.25rem;
  }

  .logo-text .brand-sub {
    font-size: 0.75rem;
  }

  .emergency-call-btn {
    padding: var(--space-xs) var(--space-md);
    font-size: 0.875rem;
  }

  .alert-text strong {
    font-size: 1rem;
  }

  .alert-text span {
    font-size: 0.75rem;
  }

  .services-grid {
    grid-template-columns: 1fr;
    gap: var(--space-lg);
  }

  .service-card {
    padding: var(--space-lg);
  }

  .service-icon {
    width: 56px;
    height: 56px;
    font-size: 1.25rem;
  }

  .footer-grid {
    grid-template-columns: 1fr;
    gap: var(--space-xl);
    text-align: center;
  }

  .footer-bottom-content {
    flex-direction: column;
    gap: var(--space-md);
    text-align: center;
  }

  .contact-grid {
    gap: var(--space-xl);
  }

  .contact-form-section {
    padding: var(--space-lg);
  }

  .about-stats {
    grid-template-columns: 1fr;
  }

  .about-features {
    flex-direction: column;
    gap: var(--space-md);
  }

  .gallery-grid {
    grid-template-columns: 1fr;
    gap: var(--space-lg);
  }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .hero-canvas {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .scroll-indicator {
    display: none;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  :root {
    --gray-50: #0f172a;
    --gray-100: #1e293b;
    --gray-200: #334155;
    --gray-300: #475569;
    --gray-400: #64748b;
    --gray-500: #94a3b8;
    --gray-600: #cbd5e1;
    --gray-700: #e2e8f0;
    --gray-800: #f1f5f9;
    --gray-900: #f8fafc;
  }

  body {
    background-color: var(--gray-50);
  }

  .navbar {
    background: rgba(15, 23, 42, 0.95);
    border-bottom-color: rgba(30, 64, 175, 0.2);
  }

  .hero-overlay {
    background: linear-gradient(
      135deg,
      rgba(30, 64, 175, 0.1) 0%,
      rgba(15, 23, 42, 0.95) 50%,
      rgba(8, 145, 178, 0.1) 100%
    );
  }

  .image-frame {
    background: linear-gradient(
      135deg,
      var(--gray-200) 0%,
      var(--gray-300) 100%
    );
  }
}

/* ===== PREMIUM ANIMATIONS ===== */
@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }
  10%,
  30%,
  50%,
  70%,
  90% {
    transform: translateX(-5px);
  }
  20%,
  40%,
  60%,
  80% {
    transform: translateX(5px);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}
